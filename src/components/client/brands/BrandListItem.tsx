import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageProps,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ThemedText } from "../../ui/ThemedText";
const BrandListItem = ({
  id,
  image,
  title,
  description,
}: {
  id: string;
  image: ImageProps | null;
  title: string;
  description: string;
}) => {
  const { currentTheme } = useTheme();

  const handlePress = () => {
    router.push(`/(client)/(screens)/brands/${id}/brand-details`);
  };

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.7}
      onPress={handlePress}
    >
      <View
        style={{
          backgroundColor: Colors[currentTheme ?? "dark"].secondary,
          justifyContent: "center",
          alignItems: "center",
          borderRadius: scale(100),
          height: scale(58),
          width: scale(58),
        }}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View style={{ marginTop: scale(8), paddingHorizontal: scale(7) }}>
        <ThemedText size={16} type="semi-bold">
          {title}
        </ThemedText>
        <ThemedText
          type="semi-bold"
          style={{ color: Colors[currentTheme ?? "dark"].secondary }}
          numberOfLines={1}
        >
          {description}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    flexDirection: "row",
    gap: scale(20),
    marginBottom: scale(20),
    marginRight: scale(10),
    width: "100%",
  },
  image: {
    width: scale(58),
    height: scale(58),
    borderRadius: scale(64),
  },
});

export default BrandListItem;
