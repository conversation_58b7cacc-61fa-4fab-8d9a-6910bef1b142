import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import {
  Image,
  ImageProps,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

const BrandCard = ({
  image,
  title,
  subtitle,
  id,
}: {
  image: ImageProps | null;
  title: string;
  subtitle: string;
  id: string;
}) => {
  const { currentTheme } = useTheme();
  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.7}
      onPress={() => {
        router.push(`/(client)/(screens)/brands/${id}/brand-details`);
      }}
    >
      <View
        style={{
          backgroundColor: Colors[currentTheme ?? "dark"].secondary,
          justifyContent: "center",
          alignItems: "center",
          borderRadius: scale(13),
        }}
      >
        <Image
          source={image || require("@/src/assets/images/icon.png")}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      {/*<View style={{ marginTop: scale(8), paddingHorizontal: scale(7) }}>
        <ThemedText size={16} type="semi-bold" style={styles.title}>
          {title}
        </ThemedText>
        <ThemedText
          size={14}
          type="regular"
          style={{ color: Colors[currentTheme ?? "dark"].secondary }}
        >
          {subtitle}
        </ThemedText>
      </View>*/}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: scale(160),
    alignItems: "flex-start",
    marginRight: scale(15),
  },
  image: {
    width: scale(160),
    height: scale(160),
    borderRadius: scale(12),
  },
  title: {
    marginBottom: scale(4),
  },
});

export default BrandCard;
