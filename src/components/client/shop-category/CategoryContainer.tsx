import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useGetCategoriesList from "@/src/services/querys/client/useGetCategoriesList";
import { FlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import React, { useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import SkeletonCategoryList from "../../loader/SkeletonCategoryList";
import { ErrorComponentSmall } from "../../ui/ErrorComponentSmall";
import { ThemedText } from "../../ui/ThemedText";
import ShopCategory from "./ShopCategory";

const { width } = Dimensions.get("window");

interface Category {
  _id: string;
  id: string;
  name: string;
  image?: any;
  isPlaceholder?: boolean;
}

// Helper to split categories into pages of 4, padding last page with placeholders
const paginateCategories = (
  data: Category[],
  pageSize: number
): Category[][] => {
  if (!data || data.length === 0) return [];

  const pages: Category[][] = [];
  for (let i = 0; i < data.length; i += pageSize) {
    let page = data.slice(i, i + pageSize);

    // Pad the last page with placeholders if needed
    while (page.length < pageSize) {
      page.push({
        _id: `id-${i}-${page.length}`,
        id: `placeholder-${i}-${page.length}`,
        name: "",
        image: null,
        isPlaceholder: true,
      });
    }
    pages.push(page);
  }
  return pages;
};

const CategoryContainer: React.FC = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const flashListRef = useRef<FlashList<Category[]>>(null);
  const [currentPage, setCurrentPage] = useState(0);

  const { data, isError, isLoading, refetch } = useGetCategoriesList();
  const transformedCategories = useMemo(() => {
    if (!data?.data || !Array.isArray(data.data)) {
      return [];
    }
    return data.data.map((category: any, index: number) => ({
      _id: category._id,
      id: category.id?.toString() || index.toString(),
      name: category.name || "",
      image: category.image || require("@/src/assets/images/icon.png"), // fallback image
    }));
  }, [data]);

  // Paginated data
  const pages = useMemo(() => {
    return paginateCategories(transformedCategories, 2);
  }, [transformedCategories]);

  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const pageIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentPage(pageIndex);
  };

  const scrollToPage = (pageIndex: number) => {
    flashListRef.current?.scrollToIndex({
      index: pageIndex,
      animated: true,
    });
  };

  const handleCategoryPress = (categoryId: string) => {
    router.push(`/(client)/(screens)/products-list?categoryId=${categoryId}`);
  };

  const renderPage = ({ item: page }: { item: Category[] }) => {
    return (
      <View style={styles.page}>
        {page.map((category) => (
          <View
            key={category.id}
            style={[
              styles.gridItem,
              category.isPlaceholder && styles.placeholderItem,
            ]}
          >
            {!category.isPlaceholder && category.name ? (
              <ShopCategory
                image={category.image}
                name={category.name}
                onPress={() => handleCategoryPress(category._id)}
              />
            ) : null}
          </View>
        ))}
      </View>
    );
  };

  const getItemType = (item: Category[]) => {
    return "category-page";
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ThemedText size={18} type="bold" style={{ marginBottom: scale(10) }}>
          {t("client.shop.shopByCategory")}
        </ThemedText>
        <View style={styles.page}>
          {Array(2)
            .fill(null)
            .map((_, index) => (
              <View key={`skeleton-${index}`} style={styles.gridItem}>
                <SkeletonCategoryList count={1} />
              </View>
            ))}
        </View>
      </View>
    );
  }

  // Show error state
  if (isError) {
    return (
      <View style={styles.container}>
        <ThemedText size={18} type="bold" style={{ marginBottom: scale(10) }}>
          {t("client.shop.shopByCategory")}
        </ThemedText>
        <ErrorComponentSmall />
      </View>
    );
  }

  // Show empty state
  if (!isLoading && pages.length === 0) {
    return (
      <View style={styles.container}>
        <ThemedText size={18} type="bold" style={{ marginBottom: scale(10) }}>
          {t("client.shop.shopByCategory")}
        </ThemedText>
        <View style={styles.emptyState}>
          <ActivityIndicator
            color={Colors[currentTheme ?? "dark"].primary}
            size={30}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ThemedText size={18} type="bold" style={{ marginBottom: scale(10) }}>
        {t("client.shop.shopByCategory")}
      </ThemedText>

      <FlashList
        ref={flashListRef}
        data={pages}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(_, index) => `page_${index}`}
        renderItem={renderPage}
        getItemType={getItemType}
        onScroll={onScroll}
        scrollEventThrottle={16}
        snapToInterval={width}
        decelerationRate="fast"
        bounces={false}
        snapToAlignment="start"
        estimatedItemSize={width}
        extraData={currentTheme} // Re-render when theme changes
        contentContainerStyle={{ paddingRight: 0 }}
      />

      {/* Dots pagination - only show if more than one page */}
      {pages.length > 1 && (
        <View
          style={[
            styles.dotsContainer,
            { backgroundColor: Colors[currentTheme ?? "dark"].background },
          ]}
        >
          {pages.map((_, index) => (
            <TouchableOpacity
              key={`dot_${index}`}
              style={[
                styles.dot,
                {
                  backgroundColor:
                    currentPage === index
                      ? Colors[currentTheme ?? "dark"].primary
                      : Colors[currentTheme ?? "dark"].border,
                },
              ]}
              onPress={() => scrollToPage(index)}
              activeOpacity={0.7}
              accessibilityLabel={`Go to page ${index + 1}`}
              accessibilityRole="button"
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: scale(10),
  },
  page: {
    width: width,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    alignItems: "center",
  },
  gridItem: {
    width: width / 2, // Full width divided by 2 for 2 columns
    marginBottom: scale(15),
  },
  placeholderItem: {
    backgroundColor: "transparent",
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: scale(8),
    height: scale(20),
    alignItems: "center",
  },
  dot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
    marginHorizontal: scale(5),
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: scale(40),
  },
});

export default CategoryContainer;
