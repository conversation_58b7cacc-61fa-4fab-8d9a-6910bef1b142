import { scale } from "@/src/_helper/Scaler";
import { MainContainerForScreens } from "@/src/components/containers/MainContainerForScreens";
import SkeletonVendorBrandDetails from "@/src/components/loader/SkeletonVendorBrandDetails";
import { DeleteConfirmationModal } from "@/src/components/model/DeleteConfirmationModal";
import EmptyBrandListUI from "@/src/components/ui/EmptyBrandListUI";
import EmptyProductList from "@/src/components/ui/EmptyProductList";
import EmptyReviewList from "@/src/components/ui/EmptyReviewList";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import VendorProductReviewCard from "@/src/components/ui/VendorProductReviewCard";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useDeleteBrandMutation from "@/src/services/querys/vendor/useDeleteBrandMutation";
import useGetBrandDetails from "@/src/services/querys/vendor/useGetBrandDetails";
import { AntDesign, MaterialIcons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import MapView, { Marker } from "react-native-maps";

interface Product {
  _id: string;
  name: string;
  description?: string;
  price?: number;
  image?: string;
  category?: string;
  brand?: string;
  [key: string]: any; // For any additional product properties
}

interface Location {
  _id?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
}

const BrandDetails: React.FC = () => {
  const { id: brandId } = useLocalSearchParams() as { id: string };
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);
  const router = useRouter();
  const [locationData, setLocationData] = useState<Location | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Fetch brand details using the custom hook
  const {
    data: brandResponse,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetBrandDetails(brandId);

  const deleteBrandMutation = useDeleteBrandMutation();
  const brandData = brandResponse?.data?.brand;
  const products = brandData?.products || [];

  // Reviews data
  const hasReviews = brandData?.reviews && brandData?.reviews.length > 0;
  const averageRating = brandData?.rating || 0;
  const reviewsCount = brandData?.reviews?.length || 0;

  // Extract location data if it exists
  React.useEffect(() => {
    if (brandData?.location) {
      setLocationData(brandData?.location);
    }
  }, [brandData]);

  const handleEditBrand = (): void => {
    // Navigate to add product screen
    router.push(`/(vendor)/(screens)/brand/${brandId}/edit-brand`);
  };

  const handleProductEdit = (productId: string): void => {
    router.push(`/(vendor)/(screens)/product/${productId}/edit-product`);
  };

  const handleRefetch = async () => await refetch();

  const handleDeleteBrand = (): void => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async (): Promise<void> => {
    try {
      await deleteBrandMutation.mutateAsync({ id: brandId });
      setShowDeleteModal(false);
      // Navigate back to brands list after successful deletion
      router.back();
    } catch (error) {
      console.error("Failed to delete brand:", error);
      setShowDeleteModal(false);
      // You might want to show an error toast here
    }
  };

  const handleDeleteCancel = (): void => {
    setShowDeleteModal(false);
  };

  const formatPhoneNumber = (phone: string): string => {
    // Add formatting logic if needed
    return phone.startsWith("+") ? phone : `+${phone}`;
  };

  const renderStars = (rating: number) =>
    Array.from({ length: 5 }, (_, index) => (
      <AntDesign
        key={index}
        name="star"
        size={scale(16)}
        color={index < rating ? Colors[theme].star : Colors[theme].no_star}
        style={styles.starIcon}
      />
    ));

  const renderReview = (review: any) => {
    const userName = review.user?.name || review.name || "Anonymous";
    const userInitial = userName.charAt(0).toUpperCase();

    return (
      <View key={review._id || review.id} style={styles.reviewContainer}>
        <View style={styles.reviewHeader}>
          <View style={styles.reviewAvatarWrapper}>
            <ThemedText
              type="semi-bold"
              style={{
                color:
                  currentTheme === "dark"
                    ? Colors.dark.black
                    : Colors.light.white,
              }}
            >
              {userInitial}
            </ThemedText>
          </View>
          <View style={styles.reviewUserInfo}>
            <ThemedText type="semi-bold" size={16}>
              {userName}
            </ThemedText>
            <ThemedText style={styles.secondaryText}>
              {review.timeAgo ||
                new Date(review.createdAt).toLocaleDateString()}
            </ThemedText>
          </View>
        </View>
        <View style={styles.starContainer}>
          {renderStars(review.rating || 0)}
        </View>
        <ThemedText size={16} style={styles.reviewText}>
          {review.comment ||
            review.review ||
            t("vendor.brandDetails.labels.noCommentProvided")}
        </ThemedText>
        {/* Show product info if this review is for a specific product */}
        {review?.product && (
          <VendorProductReviewCard product={review?.product} />
        )}
      </View>
    );
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <View style={[styles.productCard]}>
      <View style={styles.productContent}>
        <View style={styles.productImageContainer}>
          {item?.images ? (
            <Image
              source={{ uri: item?.images[0] }}
              style={styles.productImage}
              defaultSource={require("@/src/assets/images/icon.png")}
            />
          ) : (
            <View
              style={[
                styles.productImagePlaceholder,
                { backgroundColor: Colors[theme].secondary + "20" },
              ]}
            >
              <MaterialIcons
                name="image"
                size={20}
                color={Colors[theme].secondary}
              />
            </View>
          )}
        </View>
        <View style={styles.productInfo}>
          <ThemedText type="semi-bold" size={16}>
            {item?.name}
          </ThemedText>
          {item?.description && (
            <ThemedText
              size={14}
              style={{
                color: Colors[theme].secondary,
                marginTop: scale(2),
              }}
              numberOfLines={2}
            >
              {item?.description}
            </ThemedText>
          )}
          {item?.price && (
            <ThemedText
              size={14}
              type="semi-bold"
              style={{
                color: Colors[theme].primary,
                marginTop: 4,
              }}
            >
              ${item?.price.toFixed(2)}
            </ThemedText>
          )}
        </View>
      </View>
      <TouchableOpacity
        onPress={() => handleProductEdit(item?._id)}
        style={styles.editButton}
        activeOpacity={0.7}
      >
        <MaterialIcons name="edit" size={20} color={Colors[theme].secondary} />
      </TouchableOpacity>
    </View>
  );

  // Loading state
  if (isLoading) {
    return <SkeletonVendorBrandDetails />;
  }

  // Error state
  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  // No data state
  if (!brandData) {
    return <EmptyBrandListUI />;
  }
  return (
    <>
      {/* Floating Action Buttons */}
      <TouchableOpacity
        onPress={handleEditBrand}
        style={styles.floatingEditButton}
        activeOpacity={0.7}
      >
        <MaterialIcons name="edit" size={24} color={Colors[theme].primary} />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={handleDeleteBrand}
        style={styles.floatingDeleteButton}
        activeOpacity={0.7}
        disabled={deleteBrandMutation.isPending}
      >
        <MaterialIcons name="delete" size={24} color={Colors[theme].error} />
      </TouchableOpacity>

      <MainContainerForScreens>
        {/* Brand Info Section */}
        <View style={styles.brandSection}>
          <View style={styles.brandLogoContainer}>
            {brandData?.logo ? (
              <Image
                source={{ uri: brandData?.logo }}
                style={styles.brandLogoImage}
                defaultSource={require("@/src/assets/images/icon.png")}
              />
            ) : (
              <View
                style={[
                  styles.brandLogo,
                  { backgroundColor: Colors[theme].primary },
                ]}
              >
                <MaterialIcons
                  name="business"
                  size={32}
                  color={Colors[theme].background}
                />
              </View>
            )}
          </View>

          <View style={styles.brandInfo}>
            <ThemedText type="bold" size={24} style={styles.brandName}>
              {brandData?.name}
            </ThemedText>
            <ThemedText
              size={16}
              style={[styles.brandCategory, { color: Colors[theme].secondary }]}
            >
              {brandData?.category?.name}
            </ThemedText>
            <ThemedText
              size={14}
              style={[styles.brandProducts, { color: Colors[theme].secondary }]}
            >
              {brandData?.products?.length || 0}{" "}
              {t("vendor.brandDetails.labels.products")}
            </ThemedText>
            {brandData?.rating > 0 && (
              <View style={styles.ratingContainer}>
                <MaterialIcons
                  name="star"
                  size={16}
                  color={Colors[theme].warning}
                />
                <ThemedText
                  size={14}
                  style={[
                    styles.ratingText,
                    { color: Colors[theme].secondary },
                  ]}
                >
                  {brandData?.rating.toFixed(1)} ({brandData?.reviewsCount}{" "}
                  {t("vendor.brandDetails.labels.reviews")})
                </ThemedText>
              </View>
            )}
          </View>
        </View>

        {/* About Section */}
        <View style={styles.section}>
          <ThemedText type="semi-bold" size={18} style={styles.sectionTitle}>
            {t("vendor.brandDetails.sections.about")}
          </ThemedText>
          <ThemedText style={styles.aboutText}>
            {brandData?.description ||
              t("vendor.brandDetails.labels.noDescription")}
          </ThemedText>
        </View>

        {/* Contact Section */}
        <View style={styles.section}>
          <ThemedText type="semi-bold" size={18} style={styles.sectionTitle}>
            {t("vendor.brandDetails.sections.contact")}
          </ThemedText>

          {brandData?.email && (
            <View style={styles.contactItem}>
              <ThemedText
                size={14}
                style={[
                  styles.contactLabel,
                  { color: Colors[theme].secondary },
                ]}
              >
                {t("vendor.brandDetails.labels.email")}
              </ThemedText>
              <ThemedText size={14} style={styles.contactValue}>
                {brandData?.email}
              </ThemedText>
            </View>
          )}

          {brandData?.phone && (
            <View style={styles.contactItem}>
              <ThemedText
                size={14}
                style={[
                  styles.contactLabel,
                  { color: Colors[theme].secondary },
                ]}
              >
                {t("vendor.brandDetails.labels.phone")}
              </ThemedText>
              <ThemedText size={14} style={styles.contactValue}>
                {formatPhoneNumber(brandData?.phone)}
              </ThemedText>
            </View>
          )}
        </View>

        {/* Location Section */}
        {locationData && (
          <View style={styles.section}>
            <ThemedText type="semi-bold" size={18} style={styles.sectionTitle}>
              {t("vendor.brandDetails.sections.location")}
            </ThemedText>

            {/* Show address if available */}
            {locationData?.address && (
              <View style={styles.addressContainer}>
                <MaterialIcons
                  name="location-on"
                  size={16}
                  color={Colors[theme].primary}
                />
                <ThemedText style={styles.addressText}>
                  {locationData?.address}
                  {locationData?.city && `, ${locationData?.city}`}
                  {locationData?.state && `, ${locationData?.state}`}
                  {locationData?.country && `, ${locationData?.country}`}
                  {locationData?.zipCode && ` ${locationData?.zipCode}`}
                </ThemedText>
              </View>
            )}

            {/* Show map if coordinates are available */}
            {locationData?.latitude && locationData?.longitude ? (
              <View style={styles.mapContainer}>
                <MapView
                  style={styles.map}
                  initialRegion={{
                    latitude: locationData?.latitude,
                    longitude: locationData?.longitude,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                  }}
                  showsUserLocation={false}
                  showsMyLocationButton={false}
                  scrollEnabled={true}
                  zoomEnabled={true}
                >
                  <Marker
                    coordinate={{
                      latitude: locationData?.latitude,
                      longitude: locationData?.longitude,
                    }}
                    title={brandData?.name}
                    description={
                      locationData?.address ||
                      t("vendor.brandDetails.labels.brandLocation")
                    }
                  />
                </MapView>
              </View>
            ) : (
              <View
                style={[
                  styles.mapContainer,
                  { backgroundColor: Colors[theme].thirdary },
                ]}
              >
                <View style={styles.mapPlaceholder}>
                  <MaterialIcons
                    name="location-on"
                    size={48}
                    color={Colors[theme].secondary}
                  />
                  <ThemedText
                    size={14}
                    style={[styles.mapText, { color: Colors[theme].secondary }]}
                  >
                    Location details not available
                  </ThemedText>
                </View>
              </View>
            )}
          </View>
        )}

        {/* Products Section */}
        <View style={styles.section}>
          <ThemedText type="semi-bold" size={18} style={styles.sectionTitle}>
            {t("vendor.brandDetails.sections.products")} ({products.length})
          </ThemedText>

          {products.length > 0 ? (
            <FlatList
              data={products}
              renderItem={renderProductItem}
              keyExtractor={(item) => item?._id}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
              contentContainerStyle={styles.productsList}
            />
          ) : (
            <EmptyProductList />
          )}
        </View>

        {/* Reviews Section */}
        <View style={styles.section}>
          <View style={styles.reviewsTitleContainer}>
            <ThemedText type="semi-bold" size={18} style={styles.sectionTitle}>
              {t("vendor.brandDetails.sections.reviews")} ({reviewsCount})
            </ThemedText>
          </View>
          {hasReviews ? (
            brandData?.reviews.map(renderReview)
          ) : (
            <EmptyReviewList />
          )}
        </View>
      </MainContainerForScreens>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        visible={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title={t("vendor.brandDetails.modal.title")}
        message={t("vendor.brandDetails.modal.message")}
        itemName={brandData?.name}
        itemType="brand"
        confirmText={t("vendor.brandDetails.modal.confirmText")}
        cancelText={t("vendor.brandDetails.modal.cancelText")}
        isLoading={deleteBrandMutation.isPending}
      />
    </>
  );
};

const getStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    brandSection: {
      alignItems: "center",
    },
    brandLogoContainer: {
      marginBottom: scale(16),
    },
    brandLogo: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(40),
      alignItems: "center",
      justifyContent: "center",
    },
    brandLogoImage: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(40),
    },
    brandInfo: {
      alignItems: "center",
      marginBottom: scale(24),
    },
    brandName: {
      marginBottom: scale(4),
    },
    brandCategory: {
      marginBottom: scale(4),
    },
    brandProducts: {
      marginBottom: scale(8),
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: scale(4),
    },
    ratingText: {
      marginLeft: scale(4),
    },
    // Floating Action Buttons
    floatingEditButton: {
      position: "absolute",
      top: scale(50),
      right: scale(20),
      zIndex: 1000,
      padding: scale(10),
      backgroundColor: Colors[theme].background,
      borderRadius: scale(25),
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    floatingDeleteButton: {
      position: "absolute",
      top: scale(50),
      left: scale(20), // Positioned on the left side
      zIndex: 1000,
      padding: scale(10),
      backgroundColor: Colors[theme].background,
      borderRadius: scale(25),
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    section: {
      marginBottom: scale(24),
    },
    sectionTitle: {
      marginBottom: scale(12),
    },
    aboutText: {
      lineHeight: scale(20),
    },
    contactItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: scale(8),
    },
    contactLabel: {
      flex: 1,
    },
    contactValue: {
      flex: 2,
      textAlign: "right",
    },
    // Location styles
    addressContainer: {
      flexDirection: "row",
      alignItems: "flex-start",
      marginBottom: scale(12),
      paddingHorizontal: scale(4),
    },
    addressText: {
      marginLeft: scale(8),
      flex: 1,
      lineHeight: scale(20),
    },
    mapContainer: {
      height: scale(200),
      borderRadius: scale(12),
      overflow: "hidden",
    },
    map: {
      flex: 1,
    },
    mapPlaceholder: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    mapText: {
      marginTop: scale(8),
    },
    // Products styles
    productsList: {
      paddingTop: scale(8),
    },
    productCard: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      borderRadius: scale(12),
      padding: scale(16),
      marginBottom: scale(12),
    },
    productContent: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    productImageContainer: {
      width: scale(50),
      height: scale(50),
      borderRadius: scale(8),
      marginRight: scale(12),
      overflow: "hidden",
    },
    productImage: {
      width: "100%",
      height: "100%",
      borderRadius: scale(8),
    },
    productImagePlaceholder: {
      width: "100%",
      height: "100%",
      borderRadius: scale(8),
      alignItems: "center",
      justifyContent: "center",
    },
    productInfo: {
      flex: 1,
    },
    editButton: {
      width: scale(32),
      height: scale(32),
      borderRadius: scale(16),
      alignItems: "center",
      justifyContent: "center",
    },
    // Empty products styles
    emptyProductsContainer: {
      alignItems: "center",
      paddingVertical: scale(40),
      paddingHorizontal: scale(20),
    },
    emptyProductsIconContainer: {
      width: scale(80),
      height: scale(80),
      borderRadius: scale(40),
      alignItems: "center",
      justifyContent: "center",
      marginBottom: scale(16),
    },
    emptyProductsTitle: {
      marginBottom: scale(8),
      textAlign: "center",
    },
    emptyProductsText: {
      textAlign: "center",
      lineHeight: scale(20),
      marginBottom: scale(24),
    },
    emptyProductsButton: {
      paddingHorizontal: scale(24),
    },
    // Reviews styles
    reviewsTitleContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scale(10),
    },
    reviewContainer: {
      marginTop: scale(15),
      borderRadius: scale(10),
      padding: scale(15),
    },
    reviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: scale(8),
    },
    reviewAvatarWrapper: {
      width: scale(30),
      height: scale(30),
      borderRadius: scale(15),
      backgroundColor: Colors[theme].primary,
      justifyContent: "center",
      alignItems: "center",
    },
    reviewUserInfo: {
      marginLeft: scale(10),
      flex: 1,
    },
    secondaryText: {
      color: Colors[theme].secondary,
    },
    starContainer: {
      flexDirection: "row",
      marginBottom: scale(8),
    },
    starIcon: {
      marginRight: scale(2),
    },
    reviewText: {
      marginBottom: scale(10),
      lineHeight: scale(20),
    },
    reviewActions: {
      flexDirection: "row",
    },
    actionButton: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: scale(15),
    },
    actionText: {
      color: Colors[theme].secondary,
      marginLeft: scale(4),
    },
  });

export default BrandDetails;
