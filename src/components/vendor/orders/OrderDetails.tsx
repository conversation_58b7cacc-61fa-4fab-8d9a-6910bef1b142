import { scale } from "@/src/_helper/Scaler";
import DefaultButton from "@/src/components/buttons/Default";
import Loader from "@/src/components/loader/Loader";
import { ConfirmationModal } from "@/src/components/model/ConfirmationModal";
import { DeleteConfirmationModal } from "@/src/components/model/DeleteConfirmationModal";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { AppTheme, Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useChangeOrderStatus from "@/src/services/querys/vendor/useChangeOrderStatus";
import useGetOrderDetails from "@/src/services/querys/vendor/useGetOrderDetails";
import { useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import { Image, ScrollView, StyleSheet, View } from "react-native";

const OrderDetails = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const { id } = useLocalSearchParams() as { id: string };
  const theme = currentTheme ?? "dark";
  const { data, refetch, isError, isLoading } = useGetOrderDetails(id);
  const { mutate: updateOrderStatus, isPending: isUpdating } =
    useChangeOrderStatus();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showDeliveredModal, setShowDeliveredModal] = useState(false);
  const handleRefetch = async () => await refetch();
  const styles = getStyles(theme);

  const handleStatusUpdate = (newStatus: string) => {
    if (data?.data?.order) {
      // Capitalize first letter for backend
      const formattedStatus =
        newStatus.charAt(0).toUpperCase() + newStatus.slice(1).toLowerCase();
      updateOrderStatus({ id, status: formattedStatus });
    }
  };

  const handleCancelPress = () => {
    setShowCancelModal(true);
  };

  const handleCancelConfirm = () => {
    handleStatusUpdate("cancelled");
    setShowCancelModal(false);
  };

  const handleCancelCancel = () => {
    setShowCancelModal(false);
  };

  const handleDeliveredPress = () => {
    setShowDeliveredModal(true);
  };

  const handleDeliveredConfirm = () => {
    handleStatusUpdate("delivered");
    setShowDeliveredModal(false);
  };

  const handleDeliveredCancel = () => {
    setShowDeliveredModal(false);
  };

  // Loading state
  if (isLoading) {
    return <Loader />;
  }

  // Error state
  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  // No data state
  if (!data?.data?.order) {
    return (
      <ErrorComponent
        error={t("errors.orderNotFound")}
        onRetry={handleRefetch}
      />
    );
  }

  const order = data?.data?.order;

  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return null;
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return null;
    }
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return Colors[theme].green; // Green
      case "accepted":
        return Colors[theme].blue; // Blue
      case "pending":
        return Colors[theme].orange; // Orange
      case "cancelled":
        return Colors[theme].error; // Red
      default:
        return Colors[theme].secondary;
    }
  };

  return (
    <>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Products Section */}
        <View style={styles.section}>
          <ThemedText type="bold" size={18}>
            {order?.products && order.products.length > 1
              ? t("orderDetails.sections.products")
              : t("orderDetails.sections.product")}
          </ThemedText>

          {order?.products?.map(
            (
              productItem: {
                product: {
                  _id: string;
                  name?: string;
                  price?: number;
                  images?: string[];
                  brand?: {
                    _id: string;
                    name?: string;
                  };
                };
                quantity: number;
              },
              index: number
            ) => (
              <View
                key={productItem.product?._id || index}
                style={[
                  styles.productCard,
                  index > 0 && styles.productCardSpacing,
                ]}
              >
                <View style={styles.productImageContainer}>
                  {productItem.product?.images &&
                  productItem.product.images.length > 0 ? (
                    <Image
                      source={{ uri: productItem.product.images[0] }}
                      style={styles.productImage}
                      resizeMode="cover"
                    />
                  ) : (
                    <Image
                      source={require("@/src/assets/images/icon.png")} // Fallback image
                      style={styles.productImage}
                      resizeMode="cover"
                    />
                  )}
                </View>
                <View style={styles.productInfo}>
                  <View style={styles.productHeader}>
                    <ThemedText
                      type="semi-bold"
                      size={16}
                      style={styles.productName}
                    >
                      {productItem.product?.name ||
                        t("orderDetails.fallbacks.unknownProduct")}
                    </ThemedText>
                    <View style={styles.quantityBadge}>
                      <ThemedText
                        size={12}
                        type="semi-bold"
                        style={{
                          color:
                            currentTheme === "dark"
                              ? Colors.dark.black
                              : Colors.light.white,
                        }}
                      >
                        x{productItem.quantity}
                      </ThemedText>
                    </View>
                  </View>
                  <ThemedText
                    style={{
                      color: Colors[theme].secondary,
                    }}
                  >
                    {t("orderDetails.labels.brand")}:{" "}
                    {order?.brand?.name ||
                      t("orderDetails.fallbacks.unknownBrand")}
                  </ThemedText>
                  {productItem.product?.price && (
                    <View style={styles.priceContainer}>
                      <ThemedText
                        type="semi-bold"
                        size={14}
                        style={styles.priceText}
                      >
                        ${productItem.product.price.toFixed(2)}
                      </ThemedText>
                      <ThemedText size={12} style={styles.unitPriceText}>
                        {t("orderDetails.labels.unitPrice")}
                      </ThemedText>
                    </View>
                  )}
                  {productItem.quantity > 1 && productItem.product?.price && (
                    <ThemedText
                      type="semi-bold"
                      size={14}
                      style={styles.totalItemPrice}
                    >
                      {t("orderDetails.labels.totalForItem")}: $
                      {(
                        productItem.product.price * productItem.quantity
                      ).toFixed(2)}
                    </ThemedText>
                  )}
                </View>
              </View>
            )
          )}
        </View>

        {/* User Section */}
        <View style={styles.section}>
          <ThemedText type="bold" size={18} style={styles.sectionTitle}>
            {t("orderDetails.sections.customer")}
          </ThemedText>
          <View style={styles.infoCard}>
            <ThemedText type="semi-bold" size={16} style={styles.userName}>
              {order?.client?.name ||
                t("orderDetails.fallbacks.unknownCustomer")}
            </ThemedText>
            {order?.client?.email && (
              <ThemedText
                style={{
                  color: Colors[theme].secondary,
                  marginTop: scale(4),
                }}
              >
                {t("orderDetails.labels.email")}: {order.client.email}
              </ThemedText>
            )}
            <ThemedText
              style={{
                color: Colors[theme].secondary,
                marginTop: scale(4),
              }}
            >
              {t("orderDetails.labels.phone")}:{" "}
              {order?.client?.phone ||
                t("orderDetails.fallbacks.phoneNotProvided")}
            </ThemedText>
            <ThemedText
              style={{
                color: Colors[theme].secondary,
                marginTop: scale(4),
              }}
            >
              {t("orderDetails.labels.customerId")}:{" "}
              {order?.client?._id || t("orderDetails.fallbacks.notAvailable")}
            </ThemedText>
          </View>
        </View>

        {/* Delivery Section */}
        <View style={styles.section}>
          <ThemedText type="bold" size={18} style={styles.sectionTitle}>
            {t("orderDetails.sections.delivery")}
          </ThemedText>
          <View style={styles.infoCard}>
            <ThemedText type="semi-bold" size={16} style={styles.addressLabel}>
              {t("orderDetails.labels.address")}
            </ThemedText>
            <ThemedText
              style={{
                color: Colors[theme].secondary,
                lineHeight: scale(20),
              }}
            >
              {order?.location?.address ||
                t("orderDetails.fallbacks.addressNotProvided")}
            </ThemedText>
            {order?.location?.city && (
              <ThemedText
                style={{
                  color: Colors[theme].secondary,
                  marginTop: scale(4),
                }}
              >
                {[
                  order?.location?.city,
                  order?.location?.state,
                  order?.location?.country,
                  order?.location?.zipCode,
                ]
                  .filter(Boolean)
                  .join(", ")}
              </ThemedText>
            )}
            {order?.location?.type && (
              <ThemedText
                style={{
                  color: Colors[theme].secondary,
                  marginTop: scale(4),
                  fontStyle: "italic",
                }}
              >
                {t("orderDetails.labels.locationType")}:{" "}
                {order?.location?.type.charAt(0).toUpperCase() +
                  order?.location?.type.slice(1)}
              </ThemedText>
            )}
          </View>
        </View>

        {/* Order Information Section */}
        <View style={styles.section}>
          <ThemedText type="bold" size={18} style={styles.sectionTitle}>
            {t("orderDetails.sections.orderInformation")}
          </ThemedText>
          <View style={styles.infoCard}>
            <View style={styles.orderInfoRow}>
              <ThemedText type="semi-bold" size={16}>
                {t("orderDetails.labels.orderDate")}
              </ThemedText>
              <ThemedText>
                {formatDate(order?.orderDate || order?.createdAt) ||
                  t("orderDetails.fallbacks.notAvailable")}
              </ThemedText>
            </View>

            <View style={styles.separator} />

            <View style={styles.orderInfoRow}>
              <ThemedText type="semi-bold" size={16}>
                {t("orderDetails.labels.orderStatus")}
              </ThemedText>
              <ThemedText
                style={{
                  color: getStatusColor(order?.orderStatus),
                  fontWeight: "600",
                }}
              >
                {order?.orderStatus
                  ? t(`orderStatus.${order.orderStatus.toLowerCase()}`)
                  : t("orderDetails.fallbacks.notAvailable")}
              </ThemedText>
            </View>

            <View style={styles.separator} />

            <View style={styles.orderInfoRow}>
              <ThemedText type="semi-bold" size={16}>
                {t("orderDetails.labels.totalPrice")}
              </ThemedText>
              <ThemedText type="semi-bold" size={16}>
                ${order?.totalPrice?.toFixed(2) || "0.00"}
              </ThemedText>
            </View>

            <View style={styles.separator} />

            <View style={styles.orderInfoRow}>
              <ThemedText type="semi-bold" size={16}>
                {t("orderDetails.labels.orderId")}
              </ThemedText>
              <ThemedText
                numberOfLines={1}
                style={{ flex: 1, textAlign: "right" }}
              >
                {order?._id}
              </ThemedText>
            </View>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      <View style={[styles.fixedBottomContainer]}>
        {/* Render buttons based on order status */}
        {order?.orderStatus?.toLowerCase() === "pending" && (
          <View style={styles.buttonRow}>
            <DefaultButton
              title={t("orderDetails.actions.markAsAccepted")}
              onPress={() => handleStatusUpdate("accepted")}
              style={[styles.actionButton, { marginRight: scale(8) }]}
              disabled={isUpdating}
            />
            <DefaultButton
              title={t("orderDetails.actions.markAsCancelled")}
              onPress={handleCancelPress}
              color={Colors[theme].error}
              style={[styles.actionButton, { marginLeft: scale(8) }]}
              disabled={isUpdating}
            />
          </View>
        )}

        {order?.orderStatus?.toLowerCase() === "accepted" && (
          <View style={styles.buttonRow}>
            <DefaultButton
              title={t("orderDetails.actions.markAsDelivered")}
              onPress={handleDeliveredPress}
              color={Colors[theme].primary}
              style={[styles.actionButton, { marginRight: scale(8) }]}
              disabled={isUpdating}
            />
            <DefaultButton
              title={t("orderDetails.actions.markAsCancelled")}
              onPress={handleCancelPress}
              color={Colors[theme].error}
              style={[styles.actionButton, { marginLeft: scale(8) }]}
              disabled={isUpdating}
            />
          </View>
        )}
      </View>

      <DeleteConfirmationModal
        visible={showCancelModal}
        title={t("orderDetails.modal.cancelTitle")}
        message={t("orderDetails.modal.cancelMessage")}
        confirmText={t("orderDetails.modal.cancelConfirm")}
        cancelText={t("orderDetails.modal.cancelCancel")}
        onConfirm={handleCancelConfirm}
        onClose={handleCancelCancel}
        isLoading={isUpdating}
      />

      <ConfirmationModal
        visible={showDeliveredModal}
        title={t("orderDetails.modal.deliveredTitle")}
        message={t("orderDetails.modal.deliveredMessage")}
        confirmText={t("orderDetails.modal.deliveredConfirm")}
        cancelText={t("orderDetails.modal.deliveredCancel")}
        onConfirm={handleDeliveredConfirm}
        onClose={handleDeliveredCancel}
        isLoading={isUpdating}
      />
    </>
  );
};

const getStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    centered: {
      justifyContent: "center",
      alignItems: "center",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingTop: scale(20),
    },
    section: {
      marginBottom: scale(24),
    },
    sectionTitle: {
      marginBottom: scale(12),
    },
    productCard: {
      flexDirection: "row",
      padding: scale(16),
      borderRadius: scale(12),
      alignItems: "center",
    },
    productCardSpacing: {
      marginTop: scale(12),
    },
    productImageContainer: {
      width: scale(60),
      height: scale(60),
      borderRadius: scale(8),
      marginRight: scale(16),
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: Colors[theme].primary,
    },
    productImage: {
      width: scale(50),
      height: scale(50),
      borderRadius: scale(4),
    },
    productInfo: {
      flex: 1,
    },
    productHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: scale(4),
    },
    quantityBadge: {
      backgroundColor: Colors[theme].primary,
      paddingHorizontal: scale(8),
      paddingVertical: scale(2),
      borderRadius: scale(12),
      marginLeft: scale(8),
    },

    productName: {
      marginBottom: scale(4),
    },
    priceContainer: {
      flexDirection: "row",
      alignItems: "baseline",
      gap: scale(4),
      marginTop: scale(4),
    },
    priceText: {
      color: Colors[theme].primary,
    },
    unitPriceText: {
      opacity: 0.6,
    },
    totalItemPrice: {
      color: Colors[theme].secondary,
      marginTop: scale(2),
    },
    infoCard: {
      padding: scale(16),
      borderRadius: scale(12),
    },
    userName: {
      marginBottom: scale(4),
    },
    addressLabel: {
      marginBottom: scale(8),
    },
    orderInfoRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: scale(12),
    },
    separator: {
      height: 1,
      backgroundColor: Colors[theme].input_text,
      marginHorizontal: scale(-16),
    },
    bottomPadding: {
      height: scale(100), // Space for fixed button
    },
    fixedBottomContainer: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      paddingHorizontal: scale(20),
      paddingVertical: scale(20),
      paddingBottom: scale(30), // Extra padding for safe area
      backgroundColor: Colors[theme].background,
    },
    buttonRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    actionButton: {
      flex: 1,
      borderRadius: scale(12),
      paddingVertical: scale(16),
      marginTop: 0,
      marginBottom: 0,
    },
    fullWidthButton: {
      width: "100%",
      borderRadius: scale(12),
      paddingVertical: scale(16),
      marginTop: 0,
      marginBottom: 0,
    },
    deliveredContainer: {
      width: "100%",
      paddingVertical: scale(20),
      borderRadius: scale(12),
      backgroundColor: Colors[theme].input_text,
      justifyContent: "center",
      alignItems: "center",
    },
  });

export default OrderDetails;
