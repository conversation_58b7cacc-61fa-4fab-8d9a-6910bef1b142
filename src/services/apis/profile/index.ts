import { client } from "../../api";

export const getProfile = async (
  id: string | undefined,
  deviceInfo: { deviceId: string; deviceType: string } | null
) => {
  const response = await client.get(
    `/profile/profile/${id}/${deviceInfo?.deviceId}`
  );

  return response.data;
};
export const getUserProfile = async (id: string | undefined) => {
  const response = await client.get(`profile/user-profile/${id}`);

  return response.data;
};
export const changePassword = async (
  oldPassword: string,
  newPassword: string,
  confirmNewPassword: string
) => {
  const response = await client.put("profile/change-password", {
    oldPassword,
    newPassword,
    confirmNewPassword,
  });

  return response.data;
};

export const deleteAccount = async (type: "client" | "vendor") => {
  const response = await client.post("profile/delete-account", {
    type,
  });

  return response.data;
};
