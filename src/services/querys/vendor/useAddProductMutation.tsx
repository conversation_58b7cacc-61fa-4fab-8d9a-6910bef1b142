// src/hooks/useAddBrandMutation.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { AddProductPayload, ProductFormValues } from "@/src/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { addNewProduct } from "../../apis/vendor";

const useAddProductMutation = () => {
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productData: ProductFormValues) => {
      // Send the data in the exact same structure as the form
      const payload: AddProductPayload = {
        name: productData.name,
        description: productData.description,
        category: productData.category,
        brand: productData.brand,
        price: productData.price,
        images: productData.images,
      };

      return addNewProduct(payload);
    },
    onSuccess: (data) => {
      // Invalidate and refetch brand queries
      queryClient.invalidateQueries({
        queryKey: ["vendor-products"],
      });
      queryClient.invalidateQueries({
        queryKey: ["vendor-brands"],
      });
      queryClient.invalidateQueries({
        queryKey: ["dashboard-analytics"],
      });
      router.back();
      // Show success toast
      showToast(
        t("backend.success"),
        t("backend.brandAddedSuccessfully"),
        "success"
      );
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useAddProductMutation;
