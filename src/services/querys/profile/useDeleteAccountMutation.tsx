// src/api/auth.ts
import { showToast } from "@/src/_helper/toast/showToast";
import { useLanguage } from "@/src/context/LanguageContext";
import { useMutation } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { router } from "expo-router";
import { deleteAccount } from "../../apis/profile";

const useDeleteAccountMutation = () => {
  const { t } = useLanguage();
  return useMutation({
    mutationFn: ({ type }: { type: "client" | "vendor" }) =>
      deleteAccount(type),
    onSuccess: (_) => {
      showToast("Success", t("backend.yourAccountDeleted"), "success");
      router.back();
    },
    onError: (error: unknown) => {
      if (isAxiosError(error)) {
        const message =
          error.response?.data?.message || "backend.unexpectedError";
        showToast(t("backend.error"), t(message), "error");
      } else {
        showToast(t("backend.error"), t("backend.unexpectedError"), "error");
      }
    },
  });
};

export default useDeleteAccountMutation;
